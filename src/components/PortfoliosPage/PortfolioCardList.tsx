import React from "react";
import styled from "styled-components";
import CardSearch from "@/components/Card/CardSearch/CardSearch";
import CardSearchSkeleton from "@/components/Card/CardSearch/CardSearchSkeleton";
import EmptyState from "@/components/EmptyState";
import PortfolioGrid from "@/components/PortfoliosPage/PortfolioGrid";
import { PortfolioWithQueueFlag } from "@/hooks/usePortfoliosWithQueueFlag";
import Link from "next/link";
import { MoreOptionsButton } from "@/components/MoreOptionsButton";
import { PiExport } from "react-icons/pi";
import { useToast } from "@/hooks/useToast";

const Wrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(547px, 320px));
  gap: 16px;
  margin-top: 40px;

  & > * {
    min-width: 520px;
  }
`;

const EmptyContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 80px;
`;

const StyledLink = styled(Link)`
  text-decoration: none;
  transition: text-decoration 0.2s;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
`;

type PortfolioCardListProps = {
  loading: boolean;
  portfolios: PortfolioWithQueueFlag[];
  filteredPortfolios: PortfolioWithQueueFlag[];
};

export default function PortfolioCardList({
  loading,
  portfolios,
  filteredPortfolios,
}: PortfolioCardListProps) {
  const { addToast } = useToast();

  if (loading) {
    return (
      <Wrapper>
        {Array.from({ length: 4 }).map((_, i) => (
          <CardSearchSkeleton key={i} variant="portfolio" />
        ))}
      </Wrapper>
    );
  }

  if (!loading && portfolios.length === 0) {
    return (
      <EmptyContainer>
        <EmptyState
          title="Você não possui portfólios"
          subtitle='Importe seu primeiro portfólio na aba "Importações" no menu à esquerda.'
        />
      </EmptyContainer>
    );
  }

  if (!loading && portfolios.length > 0 && filteredPortfolios.length === 0) {
    return (
      <EmptyContainer>
        <EmptyState
          title="Nenhum portfólio encontrado"
          subtitle="Tente alterar os filtros ou procurar por outro portfólio."
        />
      </EmptyContainer>
    );
  }

  return (
    <Wrapper>
      {filteredPortfolios.map((portfolio) => {
        const handleExportData = async () => {
          try {
            const blob = await import("@/services/portfolioService").then((m) =>
              m.exportPortfolioData(portfolio.id)
            );
            const { downloadFile } = await import("@/utils/downloadFile");
            downloadFile(blob, `${portfolio.name}-export.csv`);
          } catch {
            addToast({
              message: "Erro ao exportar dados do portfólio",
              variant: "error",
            });
          }
        };
        return (
          <CardSearch
            key={portfolio.id}
            maxHeight="208px"
            headerPadding="16px"
            alignItems="flex-start"
            maxWidth="none"
            header={{
              title: (
                <StyledLink href={`/portfolio/${portfolio.id}`}>
                  {portfolio.name}
                </StyledLink>
              ),
              actions: (
                <MoreOptionsButton
                  navItems={[
                    {
                      key: `exportData-${portfolio.id}`,
                      label: "Exportar dados",
                      icon: <PiExport size={16} color="#6C6F80" />,
                      onClick: handleExportData,
                    },
                  ]}
                  placement="bottomRight"
                  arrowPlacement="right"
                  minWidth={160}
                />
              ),
            }}
            sections={[
              <PortfolioGrid key={portfolio.id} portfolio={portfolio} />,
            ]}
          />
        );
      })}
    </Wrapper>
  );
}
