import getEnv from "@/hooks/getEnv";
import { Fetch } from "@/interceptors/Fetch";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const TZ = "America/Sao_Paulo";
const transcendenceServiceEndpoint = getEnv("TRANSCENDENCE_SERVICE_URL");

// Helper function to format dates with timezone
function formatDateWithTimezone(dateStr: string): string {
  return dayjs.utc(dateStr).tz(TZ).format();
}

//Atendimentos iniciados/Cobranças iniciadas
//exemplo de data aceita aqui: 2025-07-31
export async function fetchFirstMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/first-messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching first messages sent metrics:", error);
    throw error;
  }
}
// Mensagens recebidas
//exemplo de data aceita aqui: 2025-07-31
export async function fetchMessagesReceivedMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/messages-received?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching messages received metrics:", error);
    throw error;
  }
}

// Respostas enviadas
//exemplo de data aceita aqui: 2025-07-31
export async function fetchAnswerMessagesSentMetrics(
  dateStart: string,
  dateEnd: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/answer-messages-sent?dateStart=${dateStart}&dateEnd=${dateEnd}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching answer messages sent metrics:", error);
    throw error;
  }
}

//Acordos Concluídos com IA
//exemplo de data aceita aqui: 2025-07-31
export async function fetchPortfolioItemsOnlyAIMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/ai-only-interaction?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items with IA 100 metrics:", error);
    throw error;
  }
}

//Negociações com interação
//exemplo de data aceita aqui: 2025-07-31
export async function fetchPortfolioItemsWithInteractionMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY"
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/with-interaction?startDate=${startDate}&endDate=${endDate}&groupByDate=${groupByDate}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items with interaction metrics:",
      error
    );
    throw error;
  }
}

//Acordos fechados
//exemplo de data aceita aqui: 2025-07-31
export async function fetchPortfolioItemsGroupedByDateMetrics(
  startDate: string,
  endDate: string,
  groupByDate: string = "DAY",
  currentStatus: string = "SUCCEED"
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/grouped-by-date?startDate=${startDate}&endDate=${endDate}&groupByDate=${groupByDate}&currentStatus=${currentStatus}`
    );

    return data;
  } catch (error) {
    console.error(
      "Error fetching portfolio items grouped by date metrics:",
      error
    );
    throw error;
  }
}

//Ticket Médio
//exemplo de data aceita aqui: 2025-07-31T02:59:59-03:00
export async function getAverageTicket(startDate?: string, endDate?: string) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();
    if (startDate) params.append("startDate", formatDateWithTimezone(startDate));
    if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/customer/average-ticket${qs}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching average ticket ", error);
    throw error;
  }
}

//Valor recuperado
//exemplo de data aceita aqui: 2025-07-31T02:59:59-03:00
export async function getTotalDealValue(startDate?: string, endDate?: string) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();
    if (startDate) params.append("startDate", formatDateWithTimezone(startDate));
    if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/deal-value${qs}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching total deal value", error);
    throw error;
  }
}

//Cobranças iniciadas por portfólio
//Tipo de data aceita: 2025-07-31
export async function getPortfolioFirstMessageMetrics(
  portfolioId: string,
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/message-hub/metrics/first-messages-sent/${portfolioId}?dateStart=${startDate}&dateEnd=${endDate}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching portfolio first message metrics:", error);
    throw error;
  }
}

//Ticket medio por portfólio
export async function getPortfolioAverageTicket(
  portfolioId: string,
  startDate?: string,
  endDate?: string
) {
  try {
    const fetch = Fetch();

    const params = new URLSearchParams();
    if (startDate) params.append("startDate", formatDateWithTimezone(startDate));
    if (endDate) params.append("endDate", formatDateWithTimezone(endDate));
    const qs = params.toString() ? `?${params.toString()}` : "";

    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/average-ticket/${portfolioId}${qs}`
    );
    return data;
  } catch (error) {
    console.error("Error fetching average ticket ", error);
    throw error;
  }
}

//Itens importados
//Tipo de data aceita: 2025-07-31
export async function fetchPortfolioItemsCreatedMetrics(
  startDate: string,
  endDate: string
) {
  try {
    const fetch = Fetch();
    const data = await fetch.get(
      `${transcendenceServiceEndpoint}/v1/business-base/metrics/portfolio/items/created?startDate=${startDate}&endDate=${endDate}`
    );

    return data;
  } catch (error) {
    console.error("Error fetching portfolio items metrics:", error);
    throw error;
  }
}
