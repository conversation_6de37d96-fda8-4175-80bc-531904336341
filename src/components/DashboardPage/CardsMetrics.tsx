"use client";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { Card } from "@/components/Card";
import { HiSparkles } from "react-icons/hi2";
import { FiMessageSquare, FiTrendingUp } from "react-icons/fi";
import { Wallet } from "lucide-react";
import dayjs from "dayjs";
import {
  fetchFirstMessagesSentMetrics,
  fetchPortfolioItemsOnlyAIMetrics,
  getAverageTicket,
  getTotalDealValue,
} from "@/services/metricsService";
import { theme } from "@/styles/theme";
import {
  CardSkeleton,
  CardsRow,
} from "../PortfolioDetailPage/PortfolioDetailSkeleton";

const CardsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  width: 100%;
`;

interface CardsMetricsProps {
  startDate?: string;
  endDate?: string;
}

export default function CardsMetrics({
  startDate,
  endDate,
}: CardsMetricsProps) {
  const [firstMessages, setFirstMessages] = useState<number>(0);
  const [aiAgreements, setAiAgreements] = useState<number>(0);
  const [averageTicket, setAverageTicket] = useState<string>("-");
  const [totalDealValue, setTotalDealValue] = useState<string>("-");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        const isTotal = !startDate && !endDate;

        const ds = isTotal
          ? dayjs()
              .subtract(1, "year")
              .startOf("day")
              .format("YYYY-MM-DDTHH:mm:ss")
          : dayjs(startDate!).startOf("day").format("YYYY-MM-DDTHH:mm:ss");
        const de = isTotal
          ? dayjs().endOf("day").format("YYYY-MM-DDTHH:mm:ss")
          : dayjs(endDate!).endOf("day").format("YYYY-MM-DDTHH:mm:ss");

        const aiPromise = fetchPortfolioItemsOnlyAIMetrics(ds, de);
        const firstPromise = fetchFirstMessagesSentMetrics(ds, de);

        const ticketPromise = getAverageTicket(
          isTotal ? undefined : startDate,
          isTotal ? undefined : endDate
        );
        const dealValuePromise = getTotalDealValue(
          isTotal ? undefined : startDate,
          isTotal ? undefined : endDate
        );

        const [aiResp, ticketResp, dealValueResp, firstResp] =
          await Promise.all([
            aiPromise as Promise<number>,

            ticketPromise as Promise<{
              averageTicket: string;
            }>,

            dealValuePromise as Promise<{
              totalDealValue: string;
            }>,

            firstPromise as Promise<{
              totalFirstMessagesSent: { total: number };
            }>,
          ]);

        setFirstMessages(firstResp?.totalFirstMessagesSent.total ?? "-");
        setAiAgreements(Number(aiResp ?? "-"));
        setAverageTicket(ticketResp?.averageTicket ?? "-");
        setTotalDealValue(dealValueResp?.totalDealValue ?? "-");
      } catch (err) {
        console.error("Erro ao buscar métricas:", err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchStats();
  }, [startDate, endDate]);

  return (
    <>
      {isLoading ? (
        <CardsRow
          style={{
            gridTemplateColumns: "1fr 1fr 1fr 1fr",
            marginTop: 0,
          }}
        >
          <CardSkeleton />
          <CardSkeleton />
          <CardSkeleton />
          <CardSkeleton />
        </CardsRow>
      ) : (
        <CardsContainer>
          <Card
            title="Cobranças iniciadas"
            subtitle="Total de cobranças realizadas"
            icon={<HiSparkles size={24} color={theme.colors.primary} />}
            value={String(firstMessages)}
          />
          <Card
            title="Acordos fechados por IA"
            subtitle="Total de acordos concluídos por IA"
            icon={<FiMessageSquare size={24} color={theme.colors.primary} />}
            value={String(aiAgreements)}
          />
          <Card
            title="Ticket médio"
            subtitle="Valor médio por acordo fechado"
            icon={<FiTrendingUp size={24} color={theme.colors.primary} />}
            value={averageTicket}
          />
          <Card
            title="Valor recuperado"
            subtitle="Total recuperado via negociações"
            icon={<Wallet size={24} color={theme.colors.primary} />}
            value={totalDealValue}
          />
        </CardsContainer>
      )}
    </>
  );
}
