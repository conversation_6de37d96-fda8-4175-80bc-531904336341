import { useMemo, useState } from "react";
import { PortfolioExecutionStatus } from "@/types/portfolio";
import { PortfolioWithQueueFlag } from "@/hooks/usePortfoliosWithQueueFlag";

export interface PortfolioFilterState {
  searchTerm: string;
  executionStatus: "all" | PortfolioExecutionStatus;
  serviceQueue: "all" | "true" | "false";
}

export function usePortfolioFilters(portfolios: PortfolioWithQueueFlag[]) {
  const [filters, setFilters] = useState<PortfolioFilterState>({
    searchTerm: "",
    executionStatus: "all",
    serviceQueue: "all",
  });

  const setSearchTerm = (s: string) =>
    setFilters((f) => ({ ...f, searchTerm: s }));
  const setSelectedExecutionStatus = (s: string) =>
    setFilters((f) => ({
      ...f,
      executionStatus: s as PortfolioExecutionStatus,
    }));
  const setSelectedServiceQueue = (s: string) =>
    setFilters((f) => ({ ...f, serviceQueue: s as "true" | "false" }));

  const filtered = useMemo(() => {
    return portfolios.filter((p) => {
      if (
        filters.searchTerm.trim() &&
        !p.name.toLowerCase().includes(filters.searchTerm.toLowerCase())
      )
        return false;

      if (
        filters.executionStatus !== "all" &&
        p.executionStatus !== filters.executionStatus
      )
        return false;

      if (filters.serviceQueue !== "all") {
        const hasQueue = p.waitingBusinessUserResponseCount > 0;
        if (
          (filters.serviceQueue === "true" && !hasQueue) ||
          (filters.serviceQueue === "false" && hasQueue)
        )
          return false;
      }
      return true;
    });
  }, [filters, portfolios]);

  return {
    filters,
    setSearchTerm,
    setSelectedExecutionStatus,
    setSelectedServiceQueue,
    filteredPortfolios: filtered,
  };
}
