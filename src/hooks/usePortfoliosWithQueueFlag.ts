/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import {
  getAllPortfolios,
  getAllPortfolioItems,
} from "@/services/portfolioService";
import { getPortfolioFirstMessageMetrics } from "@/services/metricsService";
import { Portfolio } from "@/types/portfolio";

export type PortfolioWithQueueFlag = Portfolio & {
  waitingBusinessUserResponseCount: number;
  startedAttendancesCount: number;
};

function getOneYearRange() {
  const end = new Date();
  const start = new Date();
  start.setFullYear(end.getFullYear() - 1);
  return {
    startDate: start.toISOString().split("T")[0],
    endDate: end.toISOString().split("T")[0],
  };
}

export function usePortfoliosWithQueueFlag() {
  const [data, setData] = useState<PortfolioWithQueueFlag[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let cancelled = false;
    async function fetchData() {
      setLoading(true);
      const portfolios = await getAllPortfolios();
      const { startDate, endDate } = getOneYearRange();

      const metricsPromises = portfolios.map(async (portfolio) => {
        let startedAttendancesCount = 0;
        try {
          const metrics = (await getPortfolioFirstMessageMetrics(
            portfolio.id,
            startDate,
            endDate
          )) as any;
          startedAttendancesCount = metrics?.totalFirstMessagesSent?.total ?? 0;
        } catch {
          startedAttendancesCount = 0;
        }

        let waitingBusinessUserResponseCount = 0;
        try {
          const resp = await getAllPortfolioItems(
            { portfolioId: portfolio.id, waitingBusinessUserResponse: true },
            { page: 1, limit: 9999 },
            [portfolio]
          );
          waitingBusinessUserResponseCount =
            resp && typeof resp.total === "number" ? resp.total : 0;
        } catch {
          waitingBusinessUserResponseCount = 0;
        }
        return {
          ...portfolio,
          startedAttendancesCount,
          waitingBusinessUserResponseCount,
        };
      });
      const portfoliosWithMetrics = await Promise.all(metricsPromises);
      if (!cancelled) setData(portfoliosWithMetrics);
      setLoading(false);
    }
    fetchData();
    return () => {
      cancelled = true;
    };
  }, []);

  return { data, loading };
}
